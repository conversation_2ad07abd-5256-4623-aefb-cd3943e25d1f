# coding=utf-8
"""
AI服务业务逻辑
"""

import uuid
import json


from typing import Dict, List
from django.contrib.auth.models import User
from django.db import transaction
from django.utils import timezone

from gatesidelib.common.simplelogger import SimpleLogger

from teamvision.ai.agent import AITestAgent
from teamvision.ai.data_class import TestCaseGenerationRequest, TestCaseGenerationResponse
from teamvision.project.models import Project, ProjectTestCase
from teamvision.ai.models import (
    AIGenerationSession, AIGeneratedTestCase, AIChatHistory, AIUsageStatistics
)

class AITestCaseService:
    """AI测试 服务"""
    
    @staticmethod
    def create_generation_session(request_data: Dict, user: User, ) -> AIGenerationSession:
        """创建AI生成会话"""
        
        session_id = f"ai_gen_{uuid.uuid4().hex[:12]}"
        
        session = AIGenerationSession.objects.create(
            session_id=session_id,
            user=user,
            project=request_data['project'],
            requirement_description=request_data['requirement_description'],
            generation_type=request_data['generation_type'],
            status='active'
        )
        
        # 记录系统消息
        AIChatHistory.objects.create(
            session=session,
            message_type='system',
            content=f"开始生成测试用例会话：{session.requirement_description}",
            metadata={
                'action': 'session_created',
                'parameters': {
                    'generation_type': session.generation_type,
                }
            }
        )
        return session
    
    @staticmethod
    def generate_test_cases(session: AIGenerationSession) -> TestCaseGenerationResponse:
        """生成测试用例"""
        
        try:
            # 构建生成请求
            request = TestCaseGenerationRequest(
                requirement_description=session.requirement_description,
                generation_type=session.generation_type,
            )
            
            # 记录用户请求
            case_count = 0
            AIChatHistory.objects.create(
                session=session,
                message_type='user',
                content=f"请为模块'{session.requirement_description}'生成{case_count}个{session.generation_type}测试用例",
                metadata={'request_params': request.__dict__}
            )
            
            # 调用RAGFlow生成
            response = AITestAgent.generate_test_cases_with_ragflow(request)

            if response.success:
                # 保存生成的测试用例
                with transaction.atomic():
                    generated_cases = []
                    AITestCaseService.create_ai_generated_test_case(session, generated_cases, response.test_cases)

                    # 更新会话状态
                    session.ragflow_generation_id = response.generation_id
                    session.generated_count = len(generated_cases)
                    session.status = 'completed'
                    session.completed_time = timezone.now()
                    session.metadata.update(response.metadata)
                    session.save()

                # 记录AI响应
                AIChatHistory.objects.create(
                    session=session,
                    message_type='assistant',
                    content=f"已成功生成{len(response.test_cases)}个测试用例",
                    metadata={
                        'generation_id': response.generation_id,
                        'generated_count': len(response.test_cases)
                    }
                )
                
                # 更新使用统计
                AITestCaseService._update_usage_statistics(
                    session.user, 
                    session.project, 
                    generation_count=1,
                    generated_cases_count=len(response.test_cases)
                )
                
            else:
                # 生成失败
                session.status = 'failed'
                session.save()
                
                # 记录错误消息
                AIChatHistory.objects.create(
                    session=session,
                    message_type='system',
                    content=f"测试用例生成失败: {response.message}",
                    metadata={'error': response.message}
                )
            
            return response
            
        except Exception as e:
            SimpleLogger.exception(f"生成测试用例异常: {str(e)}")
            
            # 更新会话状态
            session.status = 'failed'
            session.save()
            
            # 记录错误
            AIChatHistory.objects.create(
                session=session,
                message_type='system',
                content=f"生成过程发生异常: {str(e)}",
                metadata={'exception': str(e)}
            )
            
            return TestCaseGenerationResponse(
                success=False,
                message=f"生成异常: {str(e)}", 
                test_cases=[],
                generation_id="",
                metadata={}
            )
    
    @staticmethod
    def create_ai_generated_test_case(session: AIGenerationSession, generated_cases: List, test_case_list: List, parent_id: int = 0) -> AIGenerationSession:
        """创建AI生成的测试用例"""

        try:
            for test_case in test_case_list:
                # 确保test_case是字典类型
                if isinstance(test_case, str):
                    SimpleLogger.warning(f"测试用例数据是字符串类型，跳过: {test_case}")
                    continue

                ai_case = AIGeneratedTestCase.objects.create(
                    session=session,
                    title=test_case['title'],
                    description=test_case['description'],
                    precondition=test_case['precondition'],
                    expect_result=test_case['expect_result'],
                    priority=test_case.get('priority', 2),
                    is_group=test_case['is_group'],
                    parent=parent_id,
                    test_type=session.generation_type,
                    status='generated',
                    metadata=test_case.get('metadata', {})
                )
                generated_cases.append(ai_case)

                children = test_case.get('Children') or test_case.get('children')
                if children:
                    # 如果children是单个对象，转换为列表
                    if isinstance(children, dict):
                        children = [children]
                    AITestCaseService.create_ai_generated_test_case(session, generated_cases, children, ai_case.id)

        except Exception as e:
              SimpleLogger.exception(f"创建AI生成用例失败: {str(e)}")
              raise Exception(f"创建失败: {str(e)}")


    @staticmethod
    def accept_generated_test_case(ai_case: AIGeneratedTestCase, parent_id: int = 0) -> ProjectTestCase:
        """采纳AI生成的测试用例"""
        
        try:
            with transaction.atomic():
                # 创建正式的测试用例
                project_case = ProjectTestCase.objects.create(
                    Title=ai_case.title,
                    Desc=ai_case.description,
                    Precondition=ai_case.precondition,
                    ExpectResult=ai_case.expect_result,
                    Priority=ai_case.priority,
                    Parent=parent_id,
                    Project=ai_case.session.project,
                    IsGroup=ai_case.is_group,
                    Creator=ai_case.session.user_id,
                )
                
                # 更新AI生成用例状态
                ai_case.status = 'accepted'
                ai_case.project_test_case = project_case
                ai_case.save()
                
                # 更新会话统计
                session = ai_case.session
                session.accepted_count += 1
                session.save()
                
                # 记录采纳消息
                AIChatHistory.objects.create(
                    session=session,
                    message_type='system',
                    content=f"测试用例'{ai_case.title}'已被采纳",
                    metadata={
                        'action': 'case_accepted',
                        'ai_case_id': ai_case.id,
                        'project_case_id': project_case.id
                    }
                )
                
                # 更新使用统计
                AITestCaseService._update_usage_statistics(
                    session.user,
                    session.project,
                    accepted_cases_count=1
                )
                
                return project_case
                
        except Exception as e:
            SimpleLogger.exception(f"采纳测试用例失败: {str(e)}")
            raise Exception(f"采纳失败: {str(e)}")
    
    @staticmethod
    def batch_accept_test_cases(ai_case_ids: List[int], parent_id: int = 0) -> List[ProjectTestCase]:
        """批量采纳测试用例"""

        accepted_cases = []
        failed_cases = []

        for ai_case_id in ai_case_ids:
            try:
                ai_case = AIGeneratedTestCase.objects.get(id=ai_case_id)

                # 检查用例是否已经被采纳
                if ai_case.status == 'accepted':
                    SimpleLogger.warning(f"用例 {ai_case_id} 已经被采纳，跳过")
                    failed_cases.append({
                        'ai_case_id': ai_case_id,
                        'error': '用例已经被采纳'
                    })
                    continue

                project_case = AITestCaseService.accept_generated_test_case(ai_case, parent_id)
                accepted_cases.append(project_case)

            except AIGeneratedTestCase.DoesNotExist:
                error_msg = f"AI生成用例 {ai_case_id} 不存在"
                SimpleLogger.error(error_msg)
                failed_cases.append({
                    'ai_case_id': ai_case_id,
                    'error': error_msg
                })
                continue
            except Exception as e:
                error_msg = f"采纳用例 {ai_case_id} 失败: {str(e)}"
                SimpleLogger.exception(error_msg)
                failed_cases.append({
                    'ai_case_id': ai_case_id,
                    'error': str(e)
                })
                continue

        # 如果有失败的用例，记录详细信息
        if failed_cases:
            SimpleLogger.warning(f"批量采纳完成，成功: {len(accepted_cases)}, 失败: {len(failed_cases)}, 失败详情: {failed_cases}")

        # 如果全部失败，抛出异常
        if not accepted_cases and failed_cases:
            raise Exception(f"所有用例采纳失败，失败数量: {len(failed_cases)}")

        return accepted_cases
    
    @staticmethod
    def optimize_test_case(ai_case: AIGeneratedTestCase, optimization_type: str = 'quality') -> Dict:
        """优化测试用例"""
        
        try:
            # 构建测试用例数据
            test_case_data = {
                'title': ai_case.title,
                'description': ai_case.description,
                'precondition': ai_case.precondition,
                'expect_result': ai_case.expect_result,
                'priority': ai_case.priority,
                'test_type': ai_case.test_type,
                'tags': ai_case.tags
            }
            
            # 调用RAGFlow优化
            from teamvision.ai.ragflow_client_sdk import ragflow_agent_client
            optimized_data = AITestAgent.optimize_test_case_with_ragflow(ragflow_agent_client, test_case_data, optimization_type)
            
            # 记录优化历史
            AIChatHistory.objects.create(
                session=ai_case.session,
                message_type='user',
                content=f"请优化测试用例'{ai_case.title}'",
                metadata={
                    'action': 'optimize_request',
                    'optimization_type': optimization_type,
                    'original_case_id': ai_case.id
                }
            )
            
            AIChatHistory.objects.create(
                session=ai_case.session,
                message_type='assistant',
                content="测试用例优化完成",
                metadata={
                    'action': 'optimize_response',
                    'optimized_data': optimized_data
                }
            )
            
            return optimized_data
            
        except Exception as e:
            SimpleLogger.exception(f"测试用例优化失败: {str(e)}")
            return {'error': str(e)}

    @staticmethod
    def chat_with_ai(session: AIGenerationSession, user_message: str) -> str:
        """与AI对话"""

        try:
            # 记录用户消息
            AIChatHistory.objects.create(
                session=session,
                message_type='user',
                content=user_message
            )

            # 智能分析用户消息并生成相应响应
            ai_response = AITestAgent.generate_smart_response(user_message, session)

            # 记录AI响应
            AIChatHistory.objects.create(
                session=session,
                message_type='assistant',
                content=ai_response
            )

            # 更新使用统计
            AITestCaseService._update_usage_statistics(
                session.user,
                session.project,
                chat_messages_count=2  # 用户消息 + AI响应
            )

            return ai_response

        except Exception as e:
            SimpleLogger.exception(f"AI对话失败: {str(e)}")
            return f"对话失败: {str(e)}"


    @staticmethod
    def get_session_history(session: AIGenerationSession) -> List[Dict]:
        """获取会话历史"""
        
        history = AIChatHistory.objects.filter(session=session).order_by('created_time')
        
        return [
            {
                'id': msg.id,
                'type': msg.message_type,
                'content': msg.content,
                'created_time': msg.created_time.strftime('%Y-%m-%d %H:%M:%S'),
                'metadata': msg.metadata
            }
            for msg in history
        ]
    
    @staticmethod
    def _update_usage_statistics(user: User, project: Project, generation_count: int = 0, generated_cases_count: int = 0, accepted_cases_count: int = 0, 
                                 chat_messages_count: int = 0, api_calls_count: int = 0, api_success_count: int = 0, api_error_count: int = 0):
        """更新使用统计"""
        
        today = timezone.now().date()
        
        stats, _ = AIUsageStatistics.objects.get_or_create(
            user=user,
            project=project,
            date=today,
            defaults={
                'generation_count': 0,
                'generated_cases_count': 0,
                'accepted_cases_count': 0,
                'chat_messages_count': 0,
                'api_calls_count': 0,
                'api_success_count': 0,
                'api_error_count': 0,
                'total_generation_time': 0,
                'average_generation_time': 0.0
            }
        )
        
        # 更新统计数据
        stats.generation_count += generation_count
        stats.generated_cases_count += generated_cases_count
        stats.accepted_cases_count += accepted_cases_count
        stats.chat_messages_count += chat_messages_count
        stats.api_calls_count += api_calls_count
        stats.api_success_count += api_success_count
        stats.api_error_count += api_error_count
        
        stats.save()

    @staticmethod
    def analyze_test_coverage(project: Project, requirement_text: str) -> Dict:
        """分析测试用例库覆盖度"""
        
        try:
            # 获取项目的所有测试用例
            test_cases = ProjectTestCase.objects.filter(
                Project=project.id,
                IsGroup=False,
                Status=1
            )

            # 构建测试用例数据
            test_cases_data = []
            for case in test_cases:
                test_cases_data.append({
                    'title': case.Title,
                    'description': case.Desc,
                    'precondition': case.Precondition,
                    'expect_result': case.ExpectResult,
                    'priority': case.Priority
                })
            
            # 调用RAGFlow分析
            from teamvision.ai.ragflow_client_sdk import ragflow_agent_client
            analysis_result = AITestAgent.analyze_test_coverage_with_ragflow(
                ragflow_agent_client, requirement_text, test_cases_data
            )
            
            return analysis_result
            
        except Exception as e:
            SimpleLogger.exception(f"测试覆盖度分析失败: {str(e)}")
            return {
                'error': str(e),
                'coverage_percentage': 0,
                'missing_scenarios': [],
                'suggestions': []
            }
    
