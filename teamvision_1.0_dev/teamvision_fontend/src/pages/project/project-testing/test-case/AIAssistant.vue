<template>
  <div class="ai-assistant">
    <!-- AI助手触发按钮 -->
    <div class="ai-trigger" @click="toggleAssistant" v-if="!showAssistant">
      <Icon type="ios-chatbubbles" size="18" />
      <span>AI助手</span>
    </div>

    <!-- AI助手面板 -->
    <div class="ai-panel" v-if="showAssistant">
      <div class="ai-header">
        <div class="ai-title">
          <div class="ai-logo">
            <Icon type="ios-chatbubbles" size="16" />
          </div>
          <span>AI测试助手</span>
        </div>
        <div class="ai-actions">
          <Icon type="ios-remove" @click="minimizeAssistant" class="action-btn" />
          <Icon type="ios-close" @click="closeAssistant" class="action-btn" />
        </div>
      </div>

      <!-- 聊天区域 -->
      <div class="ai-chat-area" ref="chatArea">
        <!-- 欢迎消息 -->
        <div class="welcome-message" v-if="messages.length === 0 && !showRequirementSelector && !showGeneratedCases">
          <div class="welcome-text">
            <h3>下午好，我是您的AI测试助手。</h3>
            <p>我可以为您生成测试用例，补全缺失的测试内容, 分析当前用例的完整性和质量。</p>
          </div>

          <!-- 推荐工具 -->
          <div class="recommend-tools">
            <div class="tools-title">
              <Icon type="ios-construct" size="14" />
              <span>推荐工具</span>
            </div>
            <div class="tools-grid">
              <div class="tool-item" @click="generateTestCase">
                <Icon type="ios-create" size="20" />
                <span>生成用例</span>
              </div>
              <div class="tool-item" @click="completeCurrentCase">
                <Icon type="ios-add-circle" size="20" />
                <span>补全用例</span>
              </div>
              <div class="tool-item" @click="analyzeCase">
                <Icon type="ios-analytics" size="20" />
                <span>用例分析</span>
              </div>
            </div>
          </div>

          <div style="margin: 28px 0 10px; ">
            <span style="color: #4f586699;font-size: 14px;line-height: 24px; max-width: 300px;">当前项目:</span>
            <span>{{ currentProject.PBTitle }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">AI服务状态:</span>
            <span :class="['status-value', aiServiceStatus]">
              <Icon v-if="aiServiceStatus === 'available'" type="ios-checkmark-circle" color="#52c41a" />
              <Icon v-else-if="aiServiceStatus === 'unavailable'" type="ios-close-circle" color="#ff4d4f" />
              <Icon v-else type="ios-help-circle" color="#faad14" />
              {{ aiServiceStatus === 'available' ? '正常' : aiServiceStatus === 'unavailable' ? '不可用' : '检查中' }}
            </span>
          </div>

          <!-- AI服务状态和统计 -->
          <div class="ai-status-section" v-if="aiServiceStatus === 'available'">
            <div class="usage-stats">
              <div class="stats-title">今日使用情况</div>
              <div class="stats-grid">
                <div class="stat-item">
                  <span class="stat-number">{{ usageStats.today_usage }}</span>
                  <span class="stat-label">生成次数</span>
                </div>
                <div class="stat-item">
                  <span class="stat-number">{{ usageStats.generated_count }}</span>
                  <span class="stat-label">生成用例</span>
                </div>
                <div class="stat-item">
                  <span class="stat-number">{{ usageStats.accepted_count }}</span>
                  <span class="stat-label">已采纳</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 消息列表 -->
        <div class="message-list" v-if="messages.length > 0">
          <div v-for="(message, index) in messages" :key="index" :class="['message', message.type]">
            <div class="message-avatar">
              <Icon v-if="message.type === 'user'" type="ios-person" />
              <Icon v-else type="ios-chatbubbles" />
            </div>
            <div class="message-content">
              <div class="message-text" v-html="message.content"></div>
              <div class="message-time">{{ message.time }}</div>
            </div>
          </div>
          <!-- 需求选择器 -->
          <div class="requirement-selector" v-if="showRequirementSelector">
            <div class="requirement-list">
              <div v-for="requirement in projectRequirementList" :key="requirement.id"
                :class="['requirement-item', { active: selectedRequirement.id === requirement.id }]"
                @click="selectRequirement(requirement)">
                <div class="requirement-info">
                  <div class="requirement-title">[{{ requirement.id }}]{{ requirement.Title }}</div>
                  <div class="requirement-desc">{{ requirement.Description }}</div>
                </div>
                <Icon v-if="selectedRequirement.id === requirement.id" type="ios-checkmark-circle" class="check-icon" />
              </div>
            </div>
            <!-- 高级选项选项 -->
            <div class="advanced-config" v-if="selectedRequirement.id !== 0">
              <div class="config-header" @click="showAdvancedOptions = !showAdvancedOptions">
                <Icon :type="showAdvancedOptions ? 'ios-arrow-up' : 'ios-arrow-down'" />
                <span>高级选项</span>
              </div>
              <div class="config-content" v-if="showAdvancedOptions">
                <div class="config-row">
                  <label>生成类型:</label>
                  <Select v-model="generationConfig.generation_type" style="width: 120px;">
                    <Option value="functional">功能测试</Option>
                    <Option value="boundary">边界测试</Option>
                    <Option value="exception">异常测试</Option>
                    <Option value="integration">集成测试</Option>
                    <Option value="performance">性能测试</Option>
                    <Option value="security">安全测试</Option>
                  </Select>
                </div>
              </div>
            </div>
            <div class="selector-actions">
              <Button @click="cancelRequirementSelection">取消</Button>
              <Button type="primary" @click="confirmGenerateTestCase" :disabled="selectedRequirement.id === 0"
                :loading="isLoading">
                生成测试用例
              </Button>
            </div>
          </div>

          <!-- 生成的测试用例展示 -->
          <div class="generated-cases" v-if="showGeneratedCases">
            <div class="cases-tree">
              <el-tree ref="testCaseTree" :data="generatedTestCases" :props="treeProps" show-checkbox node-key="id"
                :default-expand-all="true" @check="handleCaseCheck">
                <template #default="{ node, data }">
                  <div class="tree-node">
                    <Icon :type="data.is_group === true ? 'ios-folder' : 'ios-document'" size="14" />
                    <span class="node-label">{{ data.title }}</span>
                    <span v-if="data.priority" class="priority-tag" :class="data.priority">P{{ data.priority }}</span>
                  </div>
                </template>
              </el-tree>
            </div>
            <div class="cases-actions">
              <div class="action-group">
                <Button @click="regenerateTestCases" :loading="isLoading">
                  <Icon type="ios-refresh" />重新生成
                </Button>
                <!-- <Button @click="analyzeCoverage" :loading="isLoading">
                  <Icon type="ios-analytics" />
                  分析覆盖度
                </Button>
                <Button @click="exportGeneratedCases" :disabled="!currentSessionId">
                  <Icon type="ios-download" />
                  导出用例
                </Button> -->
              </div>
              <div class="action-group">
                <Button type="primary" @click="showCaseLibrarySelector" :disabled="checkedCases.length === 0">
                  <Icon type="ios-add-circle" />选择用例添加到用例库 ({{ checkedCases.length }})
                </Button>
              </div>
            </div>
            <!-- 用例库目录选择框 -->
            <div v-if="showCaseLibraryDialog" class="case-library">
              <div class="selector-header">
                选择添加的用例库目录
              </div>
              <div class="case-library-selector">
                <div class="library-tree-container">
                  <el-tree ref="caseLibraryTree" :data="caseLibraryTree" :props="libraryTreeProps" node-key="id"
                    :highlight-current="true" :expand-on-click-node="false" :default-expand-all="false"
                    @node-click="selectLibraryNode" lazy :load="loadLibraryNode">
                    <template #default="{ node, data }">
                      <div class="library-tree-node">
                        <Icon :type="data.IsGroup ? 'ios-folder' : 'ios-document'" size="16" />
                        <span class="node-title">{{ data.Title }}</span>
                        <span v-if="data.IsGroup" class="node-type">目录</span>
                      </div>
                    </template>
                  </el-tree>
                </div>
              </div>
              <div class="case-library-selector-footer">
                <Button @click="cancelLibrarySelection">取消</Button>
                <Button type="primary" @click="confirmAddToLibrary"
                  :disabled="!selectedLibraryNode || !selectedLibraryNode.IsGroup" :loading="addingToLibrary">
                  确认添加
                </Button>
              </div>
            </div>
          </div>

          <!-- 加载状态 -->
          <div class="loading-message" v-if="isLoading">
            <div class="message-avatar">
              <Icon type="ios-chatbubbles" />
            </div>
            <div class="message-content">
              <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="ai-input-area">
        <div class="input-container">
          <div class="input-wrapper">
            <Input v-model="inputMessage" type="textarea" :rows="1" placeholder="欢迎来到AI测试助手，您可以问我任何问题"
              @keydown.enter.prevent="handleEnter" @keydown.ctrl.enter="sendMessage" class="message-input"
              :disabled="disableInputMessage" />
            <div class="input-actions">
              <Button type="primary" @click="sendMessage" :loading="isLoading" :disabled="!inputMessage.trim()"
                class="send-btn">
                <Icon type="ios-send" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getRequirementListApi } from '../../../../api/reuqirement'
import aiApi from '../../../../api/ai'

export default {
  name: 'AIAssistant',
  props: {
    testCaseData: {
      type: Object,
      default: () => ({})
    },
    projectID: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      showAssistant: false,
      inputMessage: '',
      disableInputMessage: false,
      isLoading: false,
      messages: [],
      // AI会话相关
      currentSessionId: null,
      aiSessionHistory: [],

      // 需求选择相关
      projectRequirementList: [],
      showRequirementSelector: false,
      selectedRequirement: {
        id: 0,
        Title: ''
      },

      // 生成配置
      generationConfig: {
        requirement: '',
        generation_type: 'functional'
      },

      // 生成的测试用例相关
      showGeneratedCases: false,
      generatedTestCases: [],
      checkedCases: [],
      aiGeneratedCases: [], // 存储AI生成的原始数据
      treeProps: {
        children: 'children',
        label: 'title',
        isLeaf: function (data, node) {
          return !data.IsGroup;
        }
      },

      // 用例库目录选择相关
      showCaseLibraryDialog: false,
      caseLibraryTree: [],
      selectedLibraryNode: null,
      addingToLibrary: false,
      libraryTreeProps: {
        children: 'children',
        label: 'title',
        isLeaf: function (data, node) {
          return !data.IsGroup;
        }
      },

      // AI功能状态
      aiServiceStatus: 'unknown', // unknown, available, unavailable
      showAdvancedOptions: false,

      // 统计信息
      usageStats: {
        generated_count: 0,
        accepted_count: 0,
        today_usage: 0
      }
    }
  },
  computed: {
    ...mapState('project', ['currentProject']),
  },
  methods: {
    toggleAssistant() {
      this.showAssistant = !this.showAssistant
    },

    minimizeAssistant() {
      this.showAssistant = false
    },

    closeAssistant() {
      this.showAssistant = false
      this.messages = []
      this.inputMessage = ''
      this.showRequirementSelector = false
      this.selectedRequirement = {
        id: 0,
        Title: ''
      }
    },

    handleEnter(event) {
      if (event.ctrlKey) {
        this.sendMessage()
      }
    },

    pushMessage(message, type = 'user') {
      const newMessage = {
        type: type,
        content: message,
        time: this.formatTime(new Date())
      }
      this.messages.push(newMessage)
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

    async sendMessage() {
      if (!this.inputMessage.trim() || this.isLoading) return

      this.pushMessage(this.inputMessage, 'user')
      const currentMessage = this.inputMessage
      this.inputMessage = ''
      this.isLoading = true

      this.$nextTick(() => {
        this.scrollToBottom()
      })

      try {
        let response

        // 对于特定消息，需要先创建会话再调用后端API
        if (currentMessage.trim() === '请帮我生成测试用例' && !this.currentSessionId) {
          // 先创建一个临时会话用于对话
          await this.createTemporarySession()
          response = await this.callAIChatAPI(currentMessage)
        } else if (this.currentSessionId) {
          // 如果有当前会话，使用对话API
          response = await this.callAIChatAPI(currentMessage)
        } else {
          // 否则使用通用AI服务
          response = await this.callAIService(currentMessage)
        }

        this.pushMessage(response, 'assistant')

        if (response.includes('请选择需求')) {
          this.showRequirementSelector = true
        }
      } catch (error) {
        console.error('AI对话失败:', error)
        this.pushMessage('抱歉，AI助手暂时无法响应，请稍后再试。错误信息：' + (error.message || '未知错误'), 'assistant')
      } finally {
        this.isLoading = false
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
    },

    async callAIService(message) {
      // 智能分析用户意图并调用相应的AI服务
      const trimmedMessage = message.trim()

      // 精确匹配"请帮我生成测试用例"消息
      if (trimmedMessage === '请帮我生成测试用例') {
        this.showRequirementSelector = true
        return this.selectedRequirementResponse()
      } else if (trimmedMessage.toLowerCase().includes('生成') && trimmedMessage.toLowerCase().includes('测试用例')) {
        return this.generateTestCaseResponse()
      } else if (trimmedMessage.toLowerCase().includes('分析') && trimmedMessage.toLowerCase().includes('用例')) {
        return this.analyzeCurrentTestCase()
      } else if (trimmedMessage.toLowerCase().includes('补全') || trimmedMessage.toLowerCase().includes('完善')) {
        return this.completeTestCase()
      } else {
        return this.generateGeneralResponse()
      }
    },

    async createTemporarySession() {
      // 创建临时会话用于对话
      try {
        const response = await aiApi.createAISessionApi({
          project: this.projectID,
          requirement_description: '',
          generation_type: 'functional',
        })

        if (response.data.code) {
          this.currentSessionId = response.data.result.session_id
        } else {
          throw new Error(response.data.message || '创建会话失败')
        }
      } catch (error) {
        console.error('创建临时会话失败:', error)
        throw error
      }
    },

    async callAIChatAPI(message) {
      // 调用真实的AI对话API
      try {
        const response = await aiApi.sendMessageToAIApi(this.currentSessionId, message)

        if (response.data.result.success) {
          return response.data.result.response
        } else {
          throw new Error(response.data.message || 'AI对话失败')
        }
      } catch (error) {
        console.error('AI对话API调用失败:', error)
        throw error
      }
    },

    generateAIResponse(message) {
      const lowerMessage = message.toLowerCase()

      if (lowerMessage.includes('生成') && lowerMessage.includes('测试用例')) {
        return this.generateTestCaseResponse()
      } else if (lowerMessage.includes('补全') || lowerMessage.includes('完善')) {
        return this.completeTestCase()
      } else {
        return this.generateGeneralResponse()
      }
    },

    // mock AI 
    selectedRequirementResponse() {
      return `
        <div class="ai-response">
          <h4>🎯 请选择需求</h4>
          <p>基于您的需求，为您生成测试用例</p>
        </div>
      `
    },

    generateTestCaseResponse() {
      return `
        <div class="ai-response">
          <h4>🎯 测试用例生成建议</h4>
          <p>基于您的需求，我为您生成以下测试用例结构：</p>
          
          <div class="test-case-template">
            <h5>📋 用例基本信息</h5>
            <ul>
              <li><strong>用例标题：</strong>用户登录功能验证</li>
              <li><strong>优先级：</strong>高</li>
              <li><strong>用例类型：</strong>功能测试</li>
            </ul>
          </div>
          
          <div class="action-buttons">
            <button class="apply-btn" onclick="handleApplyTestCase()">应用到当前用例</button>
            <button class="modify-btn" onclick="modifyTestCase()">修改建议</button>
          </div>
        </div>
      `
    },

    generateGeneralResponse() {
      return `
        <div class="ai-response">
          <p>我是您的测试用例AI助手，可以帮助您：</p>
          <ul>
            <li>🎯 生成测试用例</li>
            <li>📋 分析当前测试用例</li>
            <li>✨ 补全测试用例内容</li>
          </ul>
          <p>请告诉我您具体需要什么帮助？</p>
        </div>
      `
    },

    analyzeCurrentTestCase() {
      const currentCase = this.testCaseData
      let analysis = `
        <div class="ai-response">
          <h4>📋 当前测试用例分析</h4>
      `

      if (currentCase && currentCase.id) {
        analysis += `
          <div class="case-analysis">
            <h5>基本信息</h5>
            <ul>
              <li><strong>用例ID:</strong> ${currentCase.id}</li>
              <li><strong>标题:</strong> ${currentCase.Title || '未设置'}</li>
              <li><strong>优先级:</strong> ${currentCase.view_data?.priority || '未设置'}</li>
            </ul>

            <h5>完整度评估</h5>
            <div class="completeness-check">
        `

        const hasDesc = currentCase.Desc && currentCase.Desc.trim()
        const hasPrecondition = currentCase.Precondition && currentCase.Precondition.trim()
        const hasExpectedResult = currentCase.ExpectResult && currentCase.ExpectedResult.trim()

        analysis += `
              <div class="check-item ${hasDesc ? 'complete' : 'incomplete'}">
                ${hasDesc ? '✅' : '❌'} 用例描述: ${hasDesc ? '已完善' : '需要补充'}
              </div>
              <div class="check-item ${hasPrecondition ? 'complete' : 'incomplete'}">
                ${hasPrecondition ? '✅' : '❌'} 前置条件: ${hasPrecondition ? '已完善' : '需要补充'}
              </div>
              <div class="check-item ${hasExpectedResult ? 'complete' : 'incomplete'}">
                ${hasExpectedResult ? '✅' : '❌'} 预期结果: ${hasExpectedResult ? '已完善' : '需要补充'}
              </div>
            </div>

            <h5>改进建议</h5>
            <ul>
        `

        if (!hasDesc) {
          analysis += '<li>建议添加详细的测试步骤描述</li>'
        }
        if (!hasPrecondition) {
          analysis += '<li>建议明确前置条件和测试环境要求</li>'
        }
        if (!hasExpectedResult) {
          analysis += '<li>建议详细描述预期的测试结果</li>'
        }

        analysis += `
            </ul>
          </div>
        `
      } else {
        analysis += `
          <p>当前没有选中的测试用例，请先选择一个测试用例进行分析。</p>
        `
      }

      analysis += `
        </div>
      `

      return analysis
    },

    completeTestCase() {
      const currentCase = this.testCaseData

      if (!currentCase || !currentCase.id) {
        return `
          <div class="ai-response">
            <h4>✨ 测试用例补全</h4>
            <p>请先选择一个测试用例，我将帮您补全缺失的内容。</p>
          </div>
        `
      }

      let suggestions = `
        <div class="ai-response">
          <h4>✨ 测试用例补全建议</h4>
          <p>基于当前用例"${currentCase.Title || '未命名用例'}"，我为您提供以下补全建议：</p>
      `

      if (!currentCase.Desc || !currentCase.Desc.trim()) {
        suggestions += `
          <div class="suggestion-section">
            <h5>📝 建议的测试步骤</h5>
            <ol>
              <li>打开应用程序/网页</li>
              <li>导航到目标功能页面</li>
              <li>输入测试数据</li>
              <li>执行目标操作</li>
              <li>验证操作结果</li>
            </ol>
            <button class="apply-btn" onclick="applyDescription()">应用到描述</button>
          </div>
        `
      }

      if (!currentCase.Precondition || !currentCase.Precondition.trim()) {
        suggestions += `
          <div class="suggestion-section">
            <h5>🔧 建议的前置条件</h5>
            <ul>
              <li>系统已正常启动</li>
              <li>用户已登录系统</li>
              <li>相关权限已配置</li>
              <li>测试数据已准备</li>
            </ul>
            <button class="apply-btn" onclick="applyPrecondition()">应用到前置条件</button>
          </div>
        `
      }

      if (!currentCase.ExpectResult || !currentCase.ExpectResult.trim()) {
        suggestions += `
          <div class="suggestion-section">
            <h5>✅ 建议的预期结果</h5>
            <ul>
              <li>操作执行成功</li>
              <li>页面显示正确信息</li>
              <li>数据保存成功</li>
              <li>无错误提示</li>
            </ul>
            <button class="apply-btn" onclick="applyExpectedResult()">应用到预期结果</button>
          </div>
        `
      }

      suggestions += `
        </div>
      `

      return suggestions
    },

    generateTestCase() {
      this.inputMessage = '请帮我生成测试用例'
      this.sendMessage()
      this.disableInputMessage = true
    },

    completeCurrentCase() {
      this.inputMessage = '请帮我补全当前测试用例的缺失内容'
      this.sendMessage()
    },

    analyzeCase() {
      this.inputMessage = '请分析当前测试用例的完整性和质量'
      this.sendMessage()
    },

    loadRequirementList() {
      getRequirementListApi(this.projectID, 'Status=3,4&page_size=100&ordering=-id').then(response => {
        this.projectRequirementList = response.data.result.results
      })
    },

    selectRequirement(requirement) {
      this.selectedRequirement = requirement
    },

    cancelRequirementSelection() {
      this.messages = []
      this.showRequirementSelector = false
      this.selectedRequirement = {
        id: 0,
        Title: ''
      }
    },

    async confirmGenerateTestCase() {
      if (!this.selectedRequirement) return

      this.pushMessage(`生成基于需求"${this.selectedRequirement.Title}"的测试用例`, 'user')
      this.showRequirementSelector = false

      // sleep 10秒
      // await new Promise(resolve => setTimeout(resolve, 10000))
      this.isLoading = true
      try {
        // 设置模块名称
        this.generationConfig.requirement = this.selectedRequirement.Title || '默认需求'

        // 判断session
        if (!this.currentSessionId) {
          await this.createTemporarySession()
        }

        // 调用真实的AI生成API
        const response = await aiApi.quickGenerateTestCasesApi(
          this.currentSessionId,
          this.projectID,
          {
            requirement_id: this.selectedRequirement.id,
            requirement_description: this.generationConfig.requirement,
            generation_type: this.generationConfig.generation_type
          }
        )

        if (response.data.result.success) {
          let message = `成功生成 ${response.data.result.generated_count} 个测试用例`
          this.pushMessage(message, 'assistant')

          // 转换AI生成的数据为树形结构
          this.aiGeneratedCases = response.data.result.test_cases
          this.generatedTestCases = response.data.result.test_cases
          //this.generatedTestCases = this.convertAICasesToTreeData(response.data.result.test_cases)
          this.showGeneratedCases = true
        } else {
          throw new Error(response.dataresult.message || '生成失败')
        }
      } catch (error) {
        console.error('生成测试用例失败:', error)
        this.pushMessage('生成测试用例失败: ' + (error.response.data.result.message || '未知错误'), 'assistant')
      } finally {
        this.isLoading = false
      }
    },

    // // 将AI生成的测试用例转换为树形结构
    // convertAICasesToTreeData(aiCases) {
    //   if (!aiCases || aiCases.length === 0) return []

    //   // 按测试类型分组
    //   const groupedCases = {}
    //   aiCases.forEach((testCase, index) => {
    //     const groupKey = testCase.test_type || 'functional'
    //     if (!groupedCases[groupKey]) {
    //       groupedCases[groupKey] = []
    //     }

    //     groupedCases[groupKey].push({
    //       id: testCase.id,
    //       title: testCase.title,
    //       type: 'case',
    //       priority: this.getPriorityLabel(testCase.priority),
    //       description: testCase.description,
    //       precondition: testCase.precondition,
    //       expect_result: testCase.expect_result,
    //     })
    //   })

    //   // 转换为树形结构
    //   const treeData = []
    //   Object.keys(groupedCases).forEach(groupKey => {
    //     treeData.push({
    //       id: groupKey.id,
    //       label: this.getTestTypeLabel(groupKey),
    //       type: 'folder',
    //       children: groupedCases[groupKey]
    //     })
    //   })

    //   return treeData
    // },

    getPriorityLabel(priority) {
      const priorityMap = {
        1: 'high',
        2: 'medium',
        3: 'low'
      }
      return priorityMap[priority] || 'medium'
    },

    getTestTypeLabel(type) {
      const typeMap = {
        'functional': '功能测试',
        'boundary': '边界测试',
        'exception': '异常测试',
        'integration': '集成测试',
        'performance': '性能测试',
        'security': '安全测试'
      }
      return typeMap[type] || '功能测试'
    },

    async callGenerateTestCaseAPI(requirementId) {
      // 这个方法已被 confirmGenerateTestCase 中的真实API调用替代
      // 保留作为备用
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockData = [
            {
              id: 1,
              label: '用户登录功能测试',
              type: 'folder',
              children: [
                {
                  id: 11,
                  label: '正常登录测试',
                  type: 'case',
                  priority: 'high',
                  description: '验证用户使用正确的用户名和密码能够成功登录',
                  steps: ['打开登录页面', '输入正确的用户名和密码', '点击登录按钮'],
                  expected: '用户成功登录，跳转到主页'
                },
                {
                  id: 13,
                  label: '空用户名登录测试',
                  type: 'case',
                  priority: 'medium',
                  description: '验证空用户名无法登录',
                  steps: ['打开登录页面', '用户名留空，输入密码', '点击登录按钮'],
                  expected: '显示用户名不能为空的提示',
                  children: [
                    {
                      id: 11,
                      label: '正常登录测试',
                      type: 'case',
                      priority: 'high',
                      description: '验证用户使用正确的用户名和密码能够成功登录',
                      steps: ['打开登录页面', '输入正确的用户名和密码', '点击登录按钮'],
                      expected: '用户成功登录，跳转到主页'
                    },
                    {
                      id: 13,
                      label: '空用户名登录测试',
                      type: 'case',
                      priority: 'medium',
                      description: '验证空用户名无法登录',
                      steps: ['打开登录页面', '用户名留空，输入密码', '点击登录按钮'],
                      expected: '显示用户名不能为空的提示'
                    }
                  ]
                }
              ]
            },
            {
              id: 2,
              label: '登录安全测试',
              type: 'folder',
              children: [
                {
                  id: 21,
                  label: 'SQL注入测试',
                  type: 'case',
                  priority: 'high',
                  description: '验证登录接口防SQL注入能力',
                  steps: ['在用户名输入框输入SQL注入代码', '输入任意密码', '点击登录'],
                  expected: '系统拒绝登录，不会执行SQL注入'
                },
                {
                  id: 23,
                  label: 'SQL注入测试333333',
                  type: 'case',
                  priority: 'high',
                  description: '验证登录接口防SQL注入能力',
                  steps: ['在用户名输入框输入SQL注入代码', '输入任意密码', '点击登录'],
                  expected: '系统拒绝登录，不会执行SQL注入'
                }
              ]
            },
            {
              id: 2,
              label: '安全测试',
              type: 'folder',
              children: [
                {
                  id: 21,
                  label: 'SQL注入测试',
                  type: 'case',
                  priority: 'high',
                  description: '验证登录接口防SQL注入能力',
                  steps: ['在用户名输入框输入SQL注入代码', '输入任意密码', '点击登录'],
                  expected: '系统拒绝登录，不会执行SQL注入'
                },
                {
                  id: 23,
                  label: 'SQL注入测试333333',
                  type: 'case',
                  priority: 'high',
                  description: '验证登录接口防SQL注入能力',
                  steps: ['在用户名输入框输入SQL注入代码', '输入任意密码', '点击登录'],
                  expected: '系统拒绝登录，不会执行SQL注入'
                }
              ]
            }
          ]
          resolve(mockData)
        }, 2000)
      })
    },

    // 测试用例树相关方法
    handleCaseCheck(data, checked) {
      // 获取所有选中的测试用例（只包含叶子节点）
      const checkedNodes = this.$refs.testCaseTree.getCheckedNodes()
      this.checkedCases = checkedNodes
    },

    backToRequirementSelection() {
      this.showGeneratedCases = false
      this.showRequirementSelector = true
      this.generatedTestCases = []
      this.checkedCases = []
    },

    // 显示用例库目录选择器
    showCaseLibrarySelector() {
      if (this.checkedCases.length === 0) {
        this.$Message.warning('请先选择要添加的测试用例')
        return
      }
      this.showCaseLibraryDialog = true
      this.selectedLibraryNode = null
    },

    // 加载用例库目录树节点
    async loadLibraryNode(node, resolve) {
      try {
        let data = []

        if (node.level === 0) {
          // 加载根节点
          data = await this.loadRootLibraryNodes()
        } else {
          // 加载子节点
          data = await this.loadChildLibraryNodes(node.data.id)
        }

        resolve(data)
      } catch (error) {
        this.$Message.error('加载用例库目录失败')
        resolve([])
      } finally {
      }
    },

    // 加载根节点
    async loadRootLibraryNodes() {
      try {
        // 调用真实API获取用例库根目录
        const response = await this.$axios.get(`/api/project/${this.projectID}/testcase/lazygrouptree?Root=0`)
        return response.data.result || []
      } catch (error) {
        console.error('加载用例库根目录失败:', error)
        // 如果API失败，返回模拟数据
        return [
          {
            id: 1,
            Title: '功能测试用例',
            IsGroup: true,
            Parent: 0,
            children: [],
            isLeaf: false
          },
          {
            id: 2,
            Title: '接口测试用例',
            IsGroup: true,
            Parent: 0,
            children: [],
            isLeaf: false
          }
        ]
      }
    },

    // 加载子节点
    async loadChildLibraryNodes(parentId) {
      try {
        // 调用真实API获取子目录
        const response = await this.$axios.get(`/api/project/${this.projectID}/testcase/lazygrouptree?Root=${parentId}`)
        return response.data.result || []
      } catch (error) {
        console.error('加载用例库子目录失败:', error)
        // 如果API失败，返回模拟数据
        return [
          {
            id: parentId * 10 + 1,
            Title: '登录模块',
            IsGroup: true,
            Parent: parentId,
            children: [],
            isLeaf: false
          },
          {
            id: parentId * 10 + 2,
            Title: '用户管理模块',
            IsGroup: true,
            Parent: parentId,
            children: [],
            isLeaf: false
          },
          {
            id: parentId * 10 + 3,
            Title: '订单模块',
            IsGroup: true,
            Parent: parentId,
            children: [],
            isLeaf: false
          }
        ]
      }
    },

    // 选择用例库目录节点
    selectLibraryNode(data) {
      if (data.IsGroup) {
        this.selectedLibraryNode = data
      } else {
        this.$Message.warning('请选择目录节点，不能选择具体的测试用例')
      }
    },

    // 取消用例库选择
    cancelLibrarySelection() {
      this.showCaseLibraryDialog = false
      this.selectedLibraryNode = null
    },

    // 确认添加到用例库
    async confirmAddToLibrary() {
      if (!this.selectedLibraryNode || !this.selectedLibraryNode.IsGroup) {
        this.$Message.warning('请选择一个目录')
        return
      }

      if (this.checkedCases.length === 0) {
        this.$Message.warning('请先选择要添加的测试用例')
        return
      }

      this.pushMessage(`将选中的测试用例添加到"${this.selectedLibraryNode.Title}"`, 'assistant')

      // 保存状态，避免在重置前丢失
      const casesToAdd = [...this.checkedCases]
      const targetDirectory = { ...this.selectedLibraryNode }

      try {
        this.addingToLibrary = true

        // 调用后端API添加测试用例到指定目录
        const result = await this.callAddCasesToLibraryAPI(casesToAdd, targetDirectory.id)

        // 只有成功后才重置状态
        this.showCaseLibraryDialog = false
        this.showGeneratedCases = false
        this.generatedTestCases = []
        this.checkedCases = []
        this.selectedRequirement = {
          id: 0,
          Title: ''
        }
        this.selectedLibraryNode = null

        // 触发父组件刷新用例列表，使用保存的数据
        this.$emit('casesAdded', {
          cases: casesToAdd,
          targetDirectory: targetDirectory,
          result: result
        })

      } catch (error) {
        console.error('添加测试用例失败:', error)

        // 根据错误类型显示不同的错误信息
        let errorMessage = '添加测试用例失败'
        if (error.response && error.response.data) {
          if (error.response.data.result && error.response.data.result.message) {
            errorMessage = error.response.data.result.message
          } else if (error.response.data.message) {
            errorMessage = error.response.data.message
          }
        } else if (error.message) {
          errorMessage = error.message
        }

        this.$Message.error(errorMessage)
        this.pushMessage(`添加失败: ${errorMessage}`, 'assistant')
      } finally {
        this.addingToLibrary = false
      }
    },

    async callAddCasesToLibraryAPI(cases, targetDirectoryId) {
      try {
        // 提取AI生成的测试用例ID
        const aiCaseIds = []

        // 从选中的用例中找到对应的AI生成数据
        cases.forEach(selectedCase => {
          if (selectedCase && selectedCase.id) {
            aiCaseIds.push(selectedCase.id)
          }
        })

        if (aiCaseIds.length === 0) {
          throw new Error('未找到对应的AI生成用例')
        }

        console.log('准备采纳的用例ID:', aiCaseIds, '目标目录ID:', targetDirectoryId)

        // 调用真实的采纳API
        const response = await aiApi.batchAcceptTestCasesApi(
          aiCaseIds,
          targetDirectoryId,
        )

        console.log('API响应:', response.data)

        // 统一处理响应格式
        const result = response.data.result || response.data

        if (result.success) {
          const targetDirectoryName = this.selectedLibraryNode ? this.selectedLibraryNode.Title : '用例库'
          const successMessage = result.message + '到' + targetDirectoryName
          this.pushMessage(successMessage, 'assistant')

          return {
            success: true,
            message: result.message,
            accepted_count: result.accepted_count || aiCaseIds.length,
            project_case_ids: result.project_case_ids || []
          }
        } else {
          throw new Error(result.message || response.data.message || '采纳失败')
        }
      } catch (error) {
        console.error('采纳测试用例API调用失败:', error)

        // 增强错误信息
        if (error.response) {
          const errorData = error.response.data
          if (errorData.result && errorData.result.message) {
            throw new Error(errorData.result.message)
          } else if (errorData.message) {
            throw new Error(errorData.message)
          } else if (errorData.error) {
            throw new Error(errorData.error)
          }
        }

        throw error
      }
    },

    formatTime(date) {
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    scrollToBottom() {
      this.$nextTick(() => {
        const chatArea = this.$refs.chatArea
        if (chatArea) {
          chatArea.scrollTop = chatArea.scrollHeight
        }
      })
    },

    // AI助手相关方法
    handleApplyTestCase: function (testCaseData) {
      // 应用AI生成的测试用例数据
      this.$Message.success({
        content: "AI建议已应用到当前测试用例",
        duration: 3,
        closable: true,
      });
    },

    // AI服务初始化
    async initializeAIService() {
      try {
        // 检查AI服务状态
        const statusResponse = await aiApi.checkAIServiceStatusApi()
        this.aiServiceStatus = statusResponse.data.result.status || 'available'
      } catch (error) {
        console.warn('AI服务状态检查失败:', error)
        this.aiServiceStatus = 'unavailable'
      }
    },

    // 加载使用统计
    async loadUsageStatistics() {
      try {
        const response = await aiApi.getAIUsageStatisticsApi({
          project: this.projectID,
          date_from: new Date().toISOString().split('T')[0] // 今天
        })

        if (response.data.result && response.data.result.length > 0) {
          const todayStats = response.data.result[0]
          this.usageStats = {
            generated_count: todayStats.generated_cases_count || 0,
            accepted_count: todayStats.accepted_cases_count || 0,
            today_usage: todayStats.generation_count || 0
          }
        }
      } catch (error) {
        console.warn('加载使用统计失败:', error)
      }
    },

    // 优化测试用例
    async optimizeTestCase(caseData) {
      try {
        this.isLoading = true

        const response = await aiApi.smartOptimizeTestCaseApi(caseData.id, 'quality')

        if (response.data.result.success) {
          this.$Message.success('测试用例优化完成')
          return response.data.result.optimized_case
        } else {
          throw new Error(response.data.result.message || '优化失败')
        }
      } catch (error) {
        console.error('优化测试用例失败:', error)
        this.$Message.error('优化失败: ' + (error.message || '未知错误'))
      } finally {
        this.isLoading = false
      }
    },

    // 分析测试覆盖度
    async analyzeCoverage() {
      try {
        this.isLoading = true

        const requirementText = this.selectedRequirement.Description || this.selectedRequirement.Title || '当前项目需求'

        const response = await aiApi.analyzeProjectCoverageApi(this.projectID, requirementText)

        if (response.data.result.success) {
          const analysis = response.data.result.analysis

          const analysisMessage = {
            type: 'assistant',
            content: this.formatCoverageAnalysis(analysis),
            time: this.formatTime(new Date())
          }

          this.messages.push(analysisMessage)
          this.$Message.success('覆盖度分析完成')
        } else {
          throw new Error(response.data.result.message || '分析失败')
        }
      } catch (error) {
        console.error('分析覆盖度失败:', error)
        this.$Message.error('分析失败: ' + (error.message || '未知错误'))
      } finally {
        this.isLoading = false
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
    },

    // 格式化覆盖度分析结果
    formatCoverageAnalysis(analysis) {
      return `
        <div class="coverage-analysis">
          <h4>📊 测试覆盖度分析报告</h4>

          <div class="coverage-summary">
            <div class="coverage-item">
              <span class="coverage-label">功能覆盖度:</span>
              <span class="coverage-value">${analysis.coverage_percentage || 0}%</span>
            </div>
          </div>

          ${analysis.missing_scenarios && analysis.missing_scenarios.length > 0 ? `
            <div class="missing-scenarios">
              <h5>🔍 缺失的测试场景:</h5>
              <ul>
                ${analysis.missing_scenarios.map(scenario => `<li>${scenario}</li>`).join('')}
              </ul>
            </div>
          ` : ''}

          ${analysis.suggestions && analysis.suggestions.length > 0 ? `
            <div class="suggestions">
              <h5>💡 改进建议:</h5>
              <ul>
                ${analysis.suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
              </ul>
            </div>
          ` : ''}
        </div>
      `
    },

    // 重新生成测试用例
    async regenerateTestCases() {
      this.showGeneratedCases = false
      this.generatedTestCases = []
      this.checkedCases = []
      this.aiGeneratedCases = []
      this.pushMessage('请重新生成测试用例', 'user')
      // 重新生成
      await this.confirmGenerateTestCase()
    },

    // 导出AI生成的测试用例
    async exportGeneratedCases() {
      if (!this.currentSessionId) {
        this.$Message.warning('没有可导出的测试用例')
        return
      }

      try {
        const response = await aiApi.exportAIGeneratedCasesApi(this.currentSessionId, 'excel')

        // 创建下载链接
        const blob = new Blob([response.data.result], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `AI生成测试用例_${this.selectedRequirement.Title}_${new Date().toISOString().split('T')[0]}.xlsx`
        link.click()
        window.URL.revokeObjectURL(url)

        this.$Message.success('测试用例导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$Message.error('导出失败: ' + (error.message || '未知错误'))
      }
    }

  },

  mounted() {
    this.loadRequirementList()
    this.initializeAIService()
    this.loadUsageStatistics()
  },


}
</script>

<style scoped lang="less">
.ai-assistant {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  max-height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.ai-trigger {
  display: flex;
  align-items: center;
  gap: 6px;
  background: #5578aa;
  color: white;
  padding: 8px 16px;
  border-radius: 24px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
  transition: all 0.2s ease;
  margin: 1px 0px 0px 0px;

  &:hover {
    background: #4338ca;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
  }

  span {
    font-size: 12px;
    font-weight: 500;
  }
}

.ai-panel {
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.08);
  padding: 0;
  width: 420px;
  min-height: 700px;
  max-height: 100%;
  margin: 0 auto;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.ai-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px 12px 20px;
  background: linear-gradient(90deg, #99dac0 0%, #7cb4fa 100%);
  border-bottom: 1px solid #f0f0f0;
  border-radius: 18px 18px 0 0;
  height: 50px;

  .ai-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    font-weight: 700;
    color: #222;
    letter-spacing: 1px;

    .ai-logo {
      background: #fff;
      border-radius: 50%;
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.04);
    }

    span {
      background: linear-gradient(90deg, #0c78fb 0%, #02ff9a 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-size: 18px;
      font-weight: 700;
    }
  }

  .ai-actions {
    display: flex;
    gap: 10px;

    .action-btn {
      font-size: 18px;
      color: #6b7280;
      cursor: pointer;
      transition: color 0.2s;

      &:hover {
        color: #2563eb;
      }
    }
  }
}

.welcome-message {
  padding: 32px 0 0 0;
  text-align: center;

  .welcome-text {
    h3 {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 8px;
      background: linear-gradient(90deg, #60a5fa 0%, #6ee7b7 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    p {
      color: #6b7280;
      font-size: 15px;
      margin-bottom: 18px;
    }
  }
}

.recommend-tools {
  margin: 0 auto 18px auto;
  background: #f8fafc;
  border-radius: 14px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.03);
  padding: 14px 0 10px 0;
  width: 80%;

  .tools-title {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #2563eb;
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 10px;
    justify-content: center;
  }

  .tools-grid {
    display: flex;
    justify-content: center;
    gap: 24px;

    .tool-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 6px;
      cursor: pointer;
      padding: 10px 12px;
      border-radius: 12px;
      background: #fff;
      box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.03);
      transition: box-shadow 0.2s, background 0.2s;

      &:hover {
        background: #e0f2fe;
        box-shadow: 0 2px 8px 0 rgba(96, 165, 250, 0.10);
      }

      i {
        background: linear-gradient(135deg, #60a5fa 0%, #6ee7b7 100%);
        color: #fff;
        border-radius: 50%;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        margin-bottom: 2px;
      }

      span {
        font-size: 13px;
        color: #374151;
        font-weight: 500;
        margin-top: 2px;
      }
    }
  }
}

.ai-chat-area {
  flex: 1;
  padding: 14px;
  background: #fff;
  overflow-y: auto;
  padding-right: 4px;
  max-height: 100%;
}

.requirement-selector {
  padding: 8px 12px 8px 40px;

  .selector-header {
    margin-bottom: 4px;
    text-align: center;

    h4 {
      color: #1f2937;
      margin: 0 0 8px 0;
      font-size: 14px;
      font-weight: 600;
    }

    p {
      color: #6b7280;
      margin: 0;
      font-size: 10px;
    }
  }

  .requirement-list {
    max-height: 320px;
    overflow-y: auto;
    margin-bottom: 12px;

    .requirement-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 12px;
      margin-bottom: 8px;
      background: #f8f9fa;
      border-radius: 16px;
      cursor: pointer;
      transition: all 0.2s ease;
      border: 1px solid transparent;

      &:hover {
        background: #e3f2fd;
      }

      &.active {
        background: #e3f2fd;
        border-color: #1a73e8;
      }

      .requirement-info {
        flex: 1;

        .requirement-title {
          font-size: 12px;
          font-weight: 500;
          color: #1f2937;
          margin-bottom: 4px;
        }

        .requirement-desc {
          font-size: 10px;
          color: #6b7280;
          line-height: 1.4;
        }
      }

      .check-icon {
        color: #1a73e8;
        font-size: 18px;
      }
    }
  }

  .selector-actions {
    display: flex;
    justify-content: space-between;
    gap: 36px;
    padding-left: 36px;
    padding-right: 36px;

    .ivu-btn {
      flex: 1;
    }
  }
}

.generated-cases {
  padding: 0px 14px 14px 28px;
  overflow-y: auto;

  .cases-header {
    margin-bottom: 16px;
    text-align: center;

    h4 {
      color: #1f2937;
      margin: 0 0 8px 0;
      font-size: 18px;
      font-weight: 600;
    }

    p {
      color: #6b7280;
      margin: 0;
      font-size: 14px;
    }
  }

  .cases-tree {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 8px;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 8px;
    background: #fafafa;

    .tree-node {
      display: flex;
      align-items: center;
      gap: 6px;
      width: 100%;

      .node-label {
        flex: 1;
        font-size: 13px;
      }

      .priority-tag {
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 10px;
        font-weight: 500;

        &.high {
          background: #fee2e2;
          color: #dc2626;
        }

        &.medium {
          background: #fef3c7;
          color: #d97706;
        }

        &.low {
          background: #ecfdf5;
          color: #059669;
        }
      }
    }
  }

  .cases-actions {
    display: flex;
    justify-content: space-between;
    gap: 12px;
    flex-wrap: wrap;
    padding: 8px 16px;
    height: 40px;

    .action-group {
      display: flex;
      gap: 8px;
      align-items: center;

      .ivu-btn {
        display: flex;
        align-items: center;
        gap: 4px;

        i {
          font-size: 12px;
        }
      }
    }
  }

  // 用例库目录选择框
  .case-library {
    margin-top: 8px;
    padding-top: 8px;
    max-height: 400px;
    overflow-y: auto;

    .case-library-selector {
      .selector-header {
        padding: 20px 20px 0;
        border-bottom: 1px solid #e1e5e9;

        p {
          margin: 0 0 12px 0;
          color: #1f2937;
          font-size: 14px;
        }

        .selected-cases-info {
          display: flex;
          align-items: center;
          gap: 6px;
          padding: 8px 12px;
          background: #e3f2fd;
          border-radius: 6px;
          margin-bottom: 16px;

          i {
            color: #1a73e8;
          }

          span {
            font-size: 13px;
            color: #1565c0;
            font-weight: 500;
          }
        }
      }

      .library-tree-container {
        overflow-y: auto;
        position: relative;
        max-height: 300px;
        margin-top: 8px;
        margin-bottom: 16px;
        border: 1px solid #e1e5e9;
        border-radius: 8px;
        padding: 12px;

        .library-tree-node {
          display: flex;
          align-items: center;
          gap: 8px;
          width: 100%;

          .node-title {
            flex: 1;
            font-size: 14px;
            color: #1f2937;
          }

          .node-type {
            font-size: 12px;
            color: #6b7280;
            background: #f3f4f6;
            padding: 2px 6px;
            border-radius: 4px;
          }

          i {
            color: #1a73e8;
          }
        }

        // Element UI 树组件样式覆盖
        /deep/ .el-tree {
          background: transparent;

          .el-tree-node {
            &:focus>.el-tree-node__content {
              background-color: #e3f2fd;
            }

            .el-tree-node__content {
              height: 36px;
              padding: 0 8px;
              border-radius: 6px;
              transition: all 0.2s;

              &:hover {
                background-color: #f5f5f5;
              }

              &.is-current {
                background-color: #e3f2fd;
                color: #1a73e8;
              }
            }

            .el-tree-node__expand-icon {
              color: #6b7280;

              &.is-leaf {
                color: transparent;
              }
            }
          }
        }
      }
    }

    .case-library-selector-footer {
      padding: 8px 24px;
      display: flex;
      justify-content: space-between;
      gap: 12px;

      .ivu-btn {
        flex: 1;
      }
    }
  }
}

.bottom-tools {
  padding: 12px 16px;
  border-top: 1px solid #e1e5e9;
  background: #fafafa;

  .tool-group {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;

    .tool-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      padding: 8px 4px;
      background: white;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
      border: 1px solid #e1e5e9;

      &:hover {
        background: #e3f2fd;
        border-color: #1a73e8;
      }

      i {
        color: #1a73e8;
      }

      span {
        font-size: 10px;
        color: #6b7280;
        font-weight: 500;
        text-align: center;
      }
    }
  }
}

.message-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow-y: auto;
  max-height: 60%;

  .message {
    display: flex;
    gap: 8px;

    &.user {
      flex-direction: row-reverse;

      .message-content {
        background: #4f46e5;
        color: white;
        border-radius: 16px 16px 4px 16px;
      }

      .message-avatar {
        background: #4f46e5;
        color: white;
      }
    }

    &.assistant {
      .message-content {
        background: white;
        color: #1f2937;
        border-radius: 16px 16px 16px 4px;
        border: 1px solid #e5e7eb;
      }

      .message-avatar {
        background: #f9fafb;
        color: #4f46e5;
        border: 1px solid #e5e7eb;
      }
    }
  }

  .message-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    i {
      font-size: 14px;
    }
  }

  .message-content {
    max-width: 240px;
    padding: 8px 12px;

    .message-text {
      font-size: 13px;
      line-height: 1.4;
      word-wrap: break-word;
    }

    .message-time {
      font-size: 10px;
      opacity: 0.6;
      margin-top: 4px;
    }
  }

  .loading-message {
    display: flex;
    gap: 8px;

    .message-avatar {
      background: #f9fafb;
      color: #4f46e5;
      border: 1px solid #e5e7eb;
    }

    .message-content {
      background: white;
      color: #1f2937;
      border-radius: 16px 16px 16px 4px;
      border: 1px solid #e5e7eb;
    }
  }
}

.typing-indicator {
  display: flex;
  gap: 3px;

  span {
    width: 6px;
    height: 6px;
    background: #4f46e5;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;

    &:nth-child(1) {
      animation-delay: -0.32s;
    }

    &:nth-child(2) {
      animation-delay: -0.16s;
    }

    &:nth-child(3) {
      animation-delay: 0s;
    }
  }
}

@keyframes typing {

  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }

  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.ai-input-area {
  background: white;
  border-top: 1px solid #e1e5e9;
  height: 110px;

  .input-container {
    padding: 12px;

    .input-wrapper {
      position: relative;
      background: #f8f9fa;
      border-radius: 20px;
      border: 1px solid #e1e5e9;
      padding: 8px 12px;

      .message-input {
        /deep/ .ivu-input {
          border: none;
          background: transparent;
          resize: none;
          padding: 0;
          font-size: 14px;
          line-height: 1.5;

          &:focus {
            box-shadow: none;
          }
        }
      }

      .input-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-top: 8px;

        .attach-btn,
        .mic-btn {
          color: #6b7280;
          cursor: pointer;
          padding: 4px;
          border-radius: 4px;
          transition: all 0.2s;

          &:hover {
            color: #1a73e8;
            background: #e3f2fd;
          }
        }

        .send-btn {
          border-radius: 50%;
          height: 28px;
          width: 28px;
          padding: 0;
          background: #1a73e8;
          border-color: #1a73e8;
          margin-left: auto;

          &:hover {
            background: #1565c0;
            border-color: #1565c0;
          }

          &:disabled {
            background: #e1e5e9;
            border-color: #e1e5e9;
          }

          i {
            font-size: 12px;
          }
        }
      }
    }

    .input-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 8px;
      padding: 0 4px;

      .toggle-chat {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 12px;
        color: #6b7280;

        .ivu-switch {
          transform: scale(0.8);
        }
      }

      .upgrade-tip {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        color: #6b7280;

        .lang-tag {
          background: #e1e5e9;
          color: #374151;
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 10px;
          font-weight: 500;
        }
      }
    }
  }
}

// AI响应内容样式
/deep/ .ai-response {
  h4 {
    color: #2c3e50;
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
  }

  h5 {
    color: #495057;
    margin: 16px 0 8px 0;
    font-size: 14px;
    font-weight: 600;
  }

  p {
    color: #6c757d;
    margin: 0 0 12px 0;
    font-size: 14px;
    line-height: 1.5;
  }

  ul,
  ol {
    margin: 8px 0;
    padding-left: 20px;

    li {
      color: #495057;
      font-size: 13px;
      line-height: 1.4;
      margin-bottom: 4px;
    }
  }

  .test-case-template,
  .optimization-suggestions,
  .test-data-suggestions,
  .review-checklist,
  .case-analysis,
  .suggestion-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    margin: 12px 0;
    border-left: 3px solid #667eea;
  }

  .completeness-check {
    margin: 8px 0;

    .check-item {
      display: flex;
      align-items: center;
      padding: 4px 0;
      font-size: 13px;

      &.complete {
        color: #28a745;
      }

      &.incomplete {
        color: #dc3545;
      }
    }
  }

  .suggestion-item {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .checklist-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 13px;
    color: #495057;

    input[type="checkbox"] {
      margin: 0;
    }
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    margin-top: 16px;

    button {
      padding: 6px 12px;
      border-radius: 6px;
      border: none;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s;

      &.apply-btn {
        background: #667eea;
        color: white;

        &:hover {
          background: #5a6fd8;
        }
      }

      &.modify-btn {
        background: #f8f9fa;
        color: #495057;
        border: 1px solid #e1e5e9;

        &:hover {
          background: #e9ecef;
        }
      }
    }
  }
}

// AI状态和统计样式
.ai-status-section {
  margin: 12px 0;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;

  .usage-stats {
    .stats-title {
      font-size: 13px;
      color: #6c757d;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 8px;

      .stat-item {
        text-align: center;
        padding: 8px;
        background: white;
        border-radius: 6px;
        border: 1px solid #e9ecef;

        .stat-number {
          display: block;
          font-size: 16px;
          font-weight: 600;
          color: #495057;
        }

        .stat-label {
          font-size: 11px;
          color: #6c757d;
          margin-top: 2px;
        }
      }
    }
  }
}

.status-item {
  align-items: center;
  margin-bottom: 12px;
  margin-top: 12px;
  text-align: center;

  .status-label {
    font-size: 14px;
    font-weight: 500;
    color: #4f586699;
    line-height: 24px;
    max-width: 300px;
  }

  .status-value {
    align-items: center;
    gap: 4px;
    font-size: 13px;
    font-weight: 500;

    &.available {
      color: #28a745;
    }

    &.unavailable {
      color: #dc3545;
    }

    &.unknown {
      color: #ffc107;
    }
  }
}

// 高级选项样式
.advanced-config {
  margin: 16px 0;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;

  .config-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: #f8f9fa;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background: #e9ecef;
    }

    span {
      font-size: 14px;
      font-weight: 500;
      color: #495057;
    }
  }

  .config-content {
    padding: 12px 16px;
    background: white;

    .config-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      label {
        font-size: 14px;
        color: #495057;
        font-weight: 500;
        min-width: 80px;
      }
    }
  }
}

// 覆盖度分析样式
/deep/ .coverage-analysis {
  .coverage-summary {
    margin: 12px 0;
    padding: 12px;
    background: #e3f2fd;
    border-radius: 6px;

    .coverage-item {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .coverage-label {
        font-weight: 500;
        color: #1565c0;
      }

      .coverage-value {
        font-size: 18px;
        font-weight: 600;
        color: #0d47a1;
      }
    }
  }

  .missing-scenarios,
  .suggestions {
    margin: 16px 0;

    h5 {
      color: #495057;
      margin-bottom: 8px;
    }

    ul {
      margin: 0;
      padding-left: 20px;

      li {
        margin-bottom: 4px;
        color: #6c757d;
      }
    }
  }
}

// 滚动条样式
.ai-chat-area::-webkit-scrollbar,
.library-tree-container::-webkit-scrollbar,
.message-list::-webkit-scrollbar {
  width: 3px;
}

.ai-chat-area::-webkit-scrollbar-track,
.library-tree-container::-webkit-scrollbar-track,
.message-list::-webkit-scrollbar-track {
  background: transparent;
}

.ai-chat-area::-webkit-scrollbar-thumb,
.library-tree-container::-webkit-scrollbar-thumb,
.message-list::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 2px;
}

.ai-chat-area::-webkit-scrollbar-thumb:hover,
.library-tree-container::-webkit-scrollbar-thumb:hover,
.message-list::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>
